'use client';

import { <PERSON><PERSON><PERSON>oom } from '@/components/multiplayer/CreateRoom';
import { JoinR<PERSON> } from '@/components/multiplayer/JoinRoom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function MultiplayerPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Single Player
            </Button>
          </Link>
        </div>

        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🚩 Multiplayer Flags</h1>
          <p className="text-xl text-gray-600">Play with friends and test your flag knowledge together!</p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto">
          <CreateRoom />
          <JoinRoom />
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-500">
            Create a room to get started, or join an existing room with an invite code!
          </p>
        </div>
      </div>
    </div>
  );
}
