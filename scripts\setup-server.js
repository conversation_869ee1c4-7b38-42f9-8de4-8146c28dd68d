#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚩 Flags.games Multiplayer Server Setup');
console.log('=====================================\n');

const serverDir = path.join(__dirname, '../../flags.games_server');
const currentDir = process.cwd();

async function checkServerHealth() {
  try {
    const response = await fetch('http://localhost:3001/api/healthz');
    const data = await response.json();
    return data.status === 'ok';
  } catch {
    return false;
  }
}

async function main() {
  // Check if server directory exists
  if (!fs.existsSync(serverDir)) {
    console.log('📥 Cloning server repository...');
    try {
      execSync('git clone https://github.com/Xurify/flags.games_server.git ../flags.games_server', {
        stdio: 'inherit',
        cwd: path.dirname(currentDir)
      });
      console.log('✅ Server repository cloned successfully!\n');
    } catch (error) {
      console.error('❌ Failed to clone server repository:', error.message);
      console.log('\nPlease manually clone the repository:');
      console.log('cd .. && git clone https://github.com/Xurify/flags.games_server.git');
      return;
    }
  } else {
    console.log('✅ Server repository already exists\n');
  }

  // Check if dependencies are installed
  const nodeModulesPath = path.join(serverDir, 'node_modules');
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 Installing server dependencies...');
    try {
      execSync('pnpm install', {
        stdio: 'inherit',
        cwd: serverDir
      });
      console.log('✅ Dependencies installed successfully!\n');
    } catch (error) {
      console.error('❌ Failed to install dependencies:', error.message);
      console.log('\nPlease manually install dependencies:');
      console.log(`cd ${serverDir} && pnpm install`);
      return;
    }
  } else {
    console.log('✅ Server dependencies already installed\n');
  }

  // Check if server is running
  console.log('🔍 Checking if server is running...');
  const isRunning = await checkServerHealth();
  
  if (isRunning) {
    console.log('✅ Server is already running on http://localhost:3001\n');
  } else {
    console.log('⚠️  Server is not running\n');
    console.log('To start the server, run:');
    console.log(`cd ${serverDir} && pnpm dev\n`);
  }

  console.log('🎯 Setup Summary:');
  console.log('================');
  console.log('✅ Type definitions created');
  console.log('✅ WebSocket hook implemented');
  console.log('✅ API client created');
  console.log('✅ UI components added');
  console.log('✅ Environment configured');
  console.log('✅ Multiplayer page created');
  
  console.log('\n🚀 Next Steps:');
  console.log('==============');
  console.log('1. Start the server (if not running):');
  console.log(`   cd ${serverDir} && pnpm dev`);
  console.log('2. Start your Next.js app:');
  console.log('   pnpm dev');
  console.log('3. Navigate to http://localhost:3000/multiplayer');
  console.log('4. Create or join a room to test the integration!');
  
  console.log('\n📖 For detailed information, see MULTIPLAYER_SETUP.md');
}

main().catch(console.error);
