# Flags.games Server - Next.js Integration Guide

## Overview
This guide shows how to integrate the flags.games WebSocket server with a Next.js client application. The server provides real-time multiplayer flag guessing games with room management, user authentication, and game state synchronization.

## Server Features
- **WebSocket-based real-time communication**
- **Room management** (create, join, leave)
- **User management** with admin controls
- **Game state management** (start, pause, resume, stop)
- **Answer submission and scoring**
- **Settings management**
- **REST API endpoints** for stats and health checks

## 1. Environment Configuration

Create `.env.local` in your Next.js project:

```env
# Flags.games Server Configuration
NEXT_PUBLIC_FLAGS_SERVER_URL=ws://localhost:3001
NEXT_PUBLIC_FLAGS_API_URL=http://localhost:3001/api

# Development/Production toggle
NEXT_PUBLIC_ENVIRONMENT=development
```

## 2. TypeScript Types

Create `types/flags-game.ts`:

```typescript
// WebSocket Message Types
export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp?: number;
}

export interface CreateRoomData {
  username: string;
  userId: string;
  roomName: string;
  settings?: Partial<RoomSettings>;
}

export interface JoinRoomData {
  inviteCode: string;
  username: string;
  userId: string;
}

export interface SubmitAnswerData {
  answer: string;
  questionId?: string;
}

export interface UpdateSettingsData {
  settings: Partial<RoomSettings>;
}

export interface KickUserData {
  userId: string;
}

// Room and User Types
export interface User {
  id: string;
  username: string;
  roomId: string;
  socketId: string;
  isAdmin: boolean;
  isReady?: boolean;
  score?: number;
}

export interface RoomSettings {
  maxRoomSize: number;
  difficulty: string;
  gameMode: string;
  timeLimit?: number;
}

export interface Room {
  id: string;
  name: string;
  inviteCode: string;
  members: User[];
  maxRoomSize: number;
  settings: RoomSettings;
  gameState: 'waiting' | 'playing' | 'paused' | 'finished';
  createdAt: string;
  createdBy: string;
}

// API Response Types
export interface HealthResponse {
  status: 'ok';
  timestamp: string;
}

export interface StatsResponse {
  rooms: number;
  users: number;
  activeGames: number;
  timestamp: string;
  metrics: Record<string, unknown>;
}

export interface RoomsResponse {
  rooms: Record<string, Room>;
  count: number;
}

// Game State Types
export interface GameState {
  currentQuestion?: {
    id: string;
    flagUrl: string;
    options: string[];
    correctAnswer: string;
  };
  scores: Record<string, number>;
  timeRemaining?: number;
  roundNumber?: number;
  totalRounds?: number;
}
```

## 3. WebSocket Client Hook

Create `hooks/useFlagsGame.ts`:

```typescript
import { useState, useEffect, useRef, useCallback } from 'react';
import { WebSocketMessage, Room, User, GameState } from '@/types/flags-game';

interface UseFlagsGameOptions {
  onRoomUpdate?: (room: Room) => void;
  onUserJoined?: (user: User) => void;
  onUserLeft?: (user: User) => void;
  onGameStateUpdate?: (gameState: GameState) => void;
  onError?: (error: string) => void;
}

export function useFlagsGame(options: UseFlagsGameOptions = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [room, setRoom] = useState<Room | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const serverUrl = process.env.NEXT_PUBLIC_FLAGS_SERVER_URL || 'ws://localhost:3001';

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) return;

    try {
      wsRef.current = new WebSocket(serverUrl);
      
      wsRef.current.onopen = () => {
        setIsConnected(true);
        setError(null);
        console.log('Connected to flags.games server');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        console.log('Disconnected from flags.games server');
        
        // Auto-reconnect after 3 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, 3000);
      };

      wsRef.current.onerror = (event) => {
        setError('WebSocket connection error');
        console.error('WebSocket error:', event);
      };
    } catch (err) {
      setError('Failed to connect to server');
      console.error('Connection error:', err);
    }
  }, [serverUrl]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setRoom(null);
    setCurrentUser(null);
    setGameState(null);
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      setError('Not connected to server');
    }
  }, []);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'CONNECTION_ESTABLISHED':
        console.log('Connection established');
        break;
        
      case 'JOIN_ROOM_SUCCESS':
        setRoom(message.data.room);
        setCurrentUser(message.data.user);
        options.onRoomUpdate?.(message.data.room);
        break;
        
      case 'USER_JOINED':
        setRoom(message.data.room);
        options.onUserJoined?.(message.data.user);
        break;
        
      case 'USER_LEFT':
        setRoom(message.data.room);
        options.onUserLeft?.(message.data.user);
        break;
        
      case 'GAME_STARTED':
        setGameState(message.data.gameState);
        options.onGameStateUpdate?.(message.data.gameState);
        break;
        
      case 'GAME_STATE_UPDATE':
        setGameState(message.data.gameState);
        options.onGameStateUpdate?.(message.data.gameState);
        break;
        
      case 'ERROR':
        setError(message.data.message || 'An error occurred');
        options.onError?.(message.data.message || 'An error occurred');
        break;
        
      default:
        console.log('Unhandled message type:', message.type);
    }
  }, [options]);

  // Game Actions
  const createRoom = useCallback((data: {
    username: string;
    userId: string;
    roomName: string;
    settings?: any;
  }) => {
    sendMessage({
      type: 'CREATE_ROOM',
      data,
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const joinRoom = useCallback((data: {
    inviteCode: string;
    username: string;
    userId: string;
  }) => {
    sendMessage({
      type: 'JOIN_ROOM',
      data,
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const leaveRoom = useCallback(() => {
    sendMessage({
      type: 'LEAVE_ROOM',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const startGame = useCallback(() => {
    sendMessage({
      type: 'START_GAME',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const submitAnswer = useCallback((answer: string, questionId?: string) => {
    sendMessage({
      type: 'SUBMIT_ANSWER',
      data: { answer, questionId },
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const toggleReady = useCallback(() => {
    sendMessage({
      type: 'TOGGLE_READY',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const updateSettings = useCallback((settings: any) => {
    sendMessage({
      type: 'UPDATE_SETTINGS',
      data: { settings },
      timestamp: Date.now()
    });
  }, [sendMessage]);

  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    // State
    isConnected,
    room,
    currentUser,
    gameState,
    error,
    
    // Actions
    connect,
    disconnect,
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    submitAnswer,
    toggleReady,
    updateSettings,
  };
}
```

## 4. API Client

Create `lib/flags-api.ts`:

```typescript
import { HealthResponse, StatsResponse, RoomsResponse } from '@/types/flags-game';

const API_BASE_URL = process.env.NEXT_PUBLIC_FLAGS_API_URL || 'http://localhost:3001/api';

class FlagsApiClient {
  private async request<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async getHealth(): Promise<HealthResponse> {
    return this.request<HealthResponse>('/healthz');
  }

  async getStats(): Promise<StatsResponse> {
    return this.request<StatsResponse>('/stats');
  }

  async getRooms(): Promise<RoomsResponse> {
    return this.request<RoomsResponse>('/rooms');
  }

  async getUsers(): Promise<any> {
    return this.request('/users');
  }
}

export const flagsApi = new FlagsApiClient();
```

## 5. React Components

### Room Creation Component
Create `components/CreateRoom.tsx`:

```typescript
'use client';

import { useState } from 'react';
import { useFlagsGame } from '@/hooks/useFlagsGame';

export function CreateRoom() {
  const [username, setUsername] = useState('');
  const [roomName, setRoomName] = useState('');
  const { createRoom, isConnected } = useFlagsGame();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim() || !roomName.trim()) return;

    createRoom({
      username: username.trim(),
      userId: crypto.randomUUID(),
      roomName: roomName.trim(),
      settings: {
        maxRoomSize: 8,
        difficulty: 'medium',
        gameMode: 'classic'
      }
    });
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Create a New Room</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="username" className="block text-sm font-medium mb-1">
            Your Username
          </label>
          <input
            id="username"
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your username"
            required
          />
        </div>
        
        <div>
          <label htmlFor="roomName" className="block text-sm font-medium mb-1">
            Room Name
          </label>
          <input
            id="roomName"
            type="text"
            value={roomName}
            onChange={(e) => setRoomName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter room name"
            required
          />
        </div>
        
        <button
          type="submit"
          disabled={!isConnected}
          className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isConnected ? 'Create Room' : 'Connecting...'}
        </button>
      </form>
    </div>
  );
}
```

### Room Joining Component
Create `components/JoinRoom.tsx`:

```typescript
'use client';

import { useState } from 'react';
import { useFlagsGame } from '@/hooks/useFlagsGame';

export function JoinRoom() {
  const [username, setUsername] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const { joinRoom, isConnected } = useFlagsGame();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim() || !inviteCode.trim()) return;

    joinRoom({
      username: username.trim(),
      userId: crypto.randomUUID(),
      inviteCode: inviteCode.trim()
    });
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Join a Room</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="username" className="block text-sm font-medium mb-1">
            Your Username
          </label>
          <input
            id="username"
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your username"
            required
          />
        </div>
        
        <div>
          <label htmlFor="inviteCode" className="block text-sm font-medium mb-1">
            Invite Code
          </label>
          <input
            id="inviteCode"
            type="text"
            value={inviteCode}
            onChange={(e) => setInviteCode(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter invite code"
            required
          />
        </div>
        
        <button
          type="submit"
          disabled={!isConnected}
          className="w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isConnected ? 'Join Room' : 'Connecting...'}
        </button>
      </form>
    </div>
  );
}
```

### Game Room Component
Create `components/GameRoom.tsx`:

```typescript
'use client';

import { useFlagsGame } from '@/hooks/useFlagsGame';
import { useState } from 'react';

export function GameRoom() {
  const { 
    room, 
    currentUser, 
    gameState, 
    startGame, 
    submitAnswer, 
    toggleReady, 
    leaveRoom 
  } = useFlagsGame();
  
  const [answer, setAnswer] = useState('');

  if (!room || !currentUser) {
    return <div>Loading room...</div>;
  }

  const handleSubmitAnswer = (e: React.FormEvent) => {
    e.preventDefault();
    if (answer.trim()) {
      submitAnswer(answer.trim());
      setAnswer('');
    }
  };

  const isAdmin = currentUser.isAdmin;
  const canStartGame = isAdmin && room.members.every(member => member.isReady);

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Room Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">{room.name}</h1>
            <p className="text-gray-600">Invite Code: {room.inviteCode}</p>
          </div>
          <button
            onClick={leaveRoom}
            className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600"
          >
            Leave Room
          </button>
        </div>
      </div>

      {/* Members List */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-bold mb-4">Players ({room.members.length}/{room.maxRoomSize})</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {room.members.map((member) => (
            <div
              key={member.id}
              className={`p-3 rounded-md border ${
                member.id === currentUser.id ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="font-medium">{member.username}</div>
              <div className="text-sm text-gray-600">
                {member.isAdmin ? 'Admin' : 'Player'}
                {member.isReady && ' • Ready'}
              </div>
              {gameState?.scores && (
                <div className="text-sm font-bold text-green-600">
                  Score: {gameState.scores[member.id] || 0}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Game Controls */}
      {room.gameState === 'waiting' && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex gap-4">
            {!currentUser.isReady && (
              <button
                onClick={toggleReady}
                className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600"
              >
                Ready
              </button>
            )}
            {currentUser.isReady && (
              <button
                onClick={toggleReady}
                className="bg-yellow-500 text-white px-6 py-2 rounded-md hover:bg-yellow-600"
              >
                Not Ready
              </button>
            )}
            {canStartGame && (
              <button
                onClick={startGame}
                className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600"
              >
                Start Game
              </button>
            )}
          </div>
        </div>
      )}

      {/* Game Interface */}
      {room.gameState === 'playing' && gameState?.currentQuestion && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mb-2">Round {gameState.roundNumber}</h2>
            {gameState.timeRemaining && (
              <p className="text-lg text-gray-600">Time: {gameState.timeRemaining}s</p>
            )}
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Flag Image */}
            <div className="flex-1">
              <img
                src={gameState.currentQuestion.flagUrl}
                alt="Flag to guess"
                className="w-full max-w-md mx-auto border rounded-lg shadow-md"
              />
            </div>

            {/* Answer Options */}
            <div className="flex-1">
              <h3 className="text-xl font-bold mb-4">Which country is this?</h3>
              <form onSubmit={handleSubmitAnswer} className="space-y-3">
                {gameState.currentQuestion.options.map((option) => (
                  <button
                    key={option}
                    type="submit"
                    onClick={() => setAnswer(option)}
                    className="w-full p-3 text-left border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {option}
                  </button>
                ))}
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Game Paused */}
      {room.gameState === 'paused' && (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <h2 className="text-2xl font-bold text-yellow-600">Game Paused</h2>
          <p className="text-gray-600">Waiting for admin to resume the game...</p>
        </div>
      )}

      {/* Game Finished */}
      {room.gameState === 'finished' && (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <h2 className="text-2xl font-bold text-green-600">Game Finished!</h2>
          <div className="mt-4">
            <h3 className="text-xl font-bold mb-2">Final Scores:</h3>
            {gameState?.scores && Object.entries(gameState.scores)
              .sort(([,a], [,b]) => b - a)
              .map(([userId, score]) => {
                const user = room.members.find(m => m.id === userId);
                return (
                  <div key={userId} className="text-lg">
                    {user?.username}: {score} points
                  </div>
                );
              })
            }
          </div>
        </div>
      )}
    </div>
  );
}
```

## 6. Main Game Page

Create `app/game/page.tsx`:

```typescript
'use client';

import { useState } from 'react';
import { useFlagsGame } from '@/hooks/useFlagsGame';
import { CreateRoom } from '@/components/CreateRoom';
import { JoinRoom } from '@/components/JoinRoom';
import { GameRoom } from '@/components/GameRoom';

export default function GamePage() {
  const { room, isConnected, error } = useFlagsGame();
  const [showJoinForm, setShowJoinForm] = useState(false);

  if (!isConnected) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Connecting to server...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">Connection Error</div>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (room) {
    return <GameRoom />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🚩 Flags.games</h1>
          <p className="text-xl text-gray-600">Test your knowledge of world flags!</p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <CreateRoom />
          <JoinRoom />
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-500">
            Don't have an invite code? Create a new room and share the code with friends!
          </p>
        </div>
      </div>
    </div>
  );
}
```

## 7. Server Setup Instructions

1. **Install Dependencies:**
   ```bash
   cd flags.games_server
   pnpm install
   ```

2. **Start the Server:**
   ```bash
   pnpm dev
   ```

3. **Server will run on:** `http://localhost:3001`

## 8. Next.js Setup

1. **Install additional dependencies:**
   ```bash
   pnpm add uuid
   pnpm add -D @types/uuid
   ```

2. **Update your `next.config.js`:**
   ```javascript
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     experimental: {
       serverComponentsExternalPackages: ['uuid']
     }
   }

   module.exports = nextConfig
   ```

## 9. Usage Example

```typescript
// In your Next.js app
import { useFlagsGame } from '@/hooks/useFlagsGame';

function MyGameComponent() {
  const { 
    isConnected, 
    room, 
    createRoom, 
    joinRoom, 
    startGame, 
    submitAnswer 
  } = useFlagsGame();

  // Use the hook methods to interact with the game
}
```

## 10. Production Deployment

For production, update your environment variables:

```env
NEXT_PUBLIC_FLAGS_SERVER_URL=wss://your-server-domain.com
NEXT_PUBLIC_FLAGS_API_URL=https://your-server-domain.com/api
NEXT_PUBLIC_ENVIRONMENT=production
```

Make sure your server is configured with proper SSL certificates for WebSocket connections.

## Key Features Implemented

✅ **Real-time WebSocket communication**  
✅ **Room creation and joining**  
✅ **User management and admin controls**  
✅ **Game state synchronization**  
✅ **Answer submission and scoring**  
✅ **Ready state management**  
✅ **Error handling and reconnection**  
✅ **TypeScript support**  
✅ **Responsive UI components**  

This integration provides a complete multiplayer flags game experience with your Next.js frontend! 