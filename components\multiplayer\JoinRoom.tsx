'use client';

import { useState } from 'react';
import { useFlagsGame } from '@/lib/hooks/useFlagsGame';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface JoinRoomProps {
  onRoomJoined?: () => void;
}

export function JoinRoom({ onRoomJoined }: JoinRoomProps) {
  const [inviteCode, setInviteCode] = useState('');
  const [username, setUsername] = useState('');
  const [isJoining, setIsJoining] = useState(false);

  const { joinRoom, isConnected, error } = useFlagsGame();

  const handleJoinRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inviteCode.trim() || !username.trim()) {
      return;
    }

    setIsJoining(true);
    
    try {
      const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      joinRoom({
        inviteCode: inviteCode.trim().toUpperCase(),
        username: username.trim(),
        userId,
      });

      onRoomJoined?.();
    } catch (err) {
      console.error('Failed to join room:', err);
    } finally {
      setIsJoining(false);
    }
  };

  const handleInviteCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Auto-uppercase and limit to 6 characters
    const value = e.target.value.toUpperCase().slice(0, 6);
    setInviteCode(value);
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Join Room</CardTitle>
        <CardDescription>
          Enter an invite code to join an existing game
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleJoinRoom} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Your Name</Label>
            <Input
              id="username"
              type="text"
              placeholder="Enter your name"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="inviteCode">Invite Code</Label>
            <Input
              id="inviteCode"
              type="text"
              placeholder="ABCDEF"
              value={inviteCode}
              onChange={handleInviteCodeChange}
              className="text-center text-lg font-mono tracking-widest"
              maxLength={6}
              required
            />
            <p className="text-xs text-gray-500">
              Enter the 6-character room code
            </p>
          </div>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}

          <Button 
            type="submit" 
            className="w-full" 
            disabled={!isConnected || isJoining || !inviteCode.trim() || !username.trim()}
          >
            {isJoining ? 'Joining...' : 'Join Room'}
          </Button>

          {!isConnected && (
            <p className="text-sm text-gray-500 text-center">
              Connecting to server...
            </p>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
