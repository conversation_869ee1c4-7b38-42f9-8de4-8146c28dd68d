import { useState, useEffect, useRef, useCallback } from 'react';
import { 
  WebSocketMessage, 
  Room, 
  User, 
  MultiplayerGameState,
  CreateRoomData,
  JoinRoomData,
  SubmitAnswerData,
  UpdateSettingsData
} from '@/lib/types/multiplayer';

interface UseFlagsGameOptions {
  onRoomUpdate?: (room: Room) => void;
  onUserJoined?: (user: User) => void;
  onUserLeft?: (user: User) => void;
  onGameStateUpdate?: (gameState: MultiplayerGameState) => void;
  onError?: (error: string) => void;
}

export function useFlagsGame(options: UseFlagsGameOptions = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [room, setRoom] = useState<Room | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [gameState, setGameState] = useState<MultiplayerGameState | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const serverUrl = process.env.NEXT_PUBLIC_FLAGS_SERVER_URL || 'ws://localhost:3001';

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) return;

    try {
      wsRef.current = new WebSocket(serverUrl);
      
      wsRef.current.onopen = () => {
        setIsConnected(true);
        setError(null);
        console.log('Connected to flags.games server');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        console.log('Disconnected from flags.games server');
        
        // Auto-reconnect after 3 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, 3000);
      };

      wsRef.current.onerror = (event) => {
        setError('WebSocket connection error');
        console.error('WebSocket error:', event);
      };
    } catch (err) {
      setError('Failed to connect to server');
      console.error('Connection error:', err);
    }
  }, [serverUrl]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setRoom(null);
    setCurrentUser(null);
    setGameState(null);
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      setError('Not connected to server');
    }
  }, []);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'CONNECTION_ESTABLISHED':
        console.log('Connection established');
        break;
        
      case 'CREATE_ROOM_SUCCESS':
        setRoom(message.data.room);
        setCurrentUser(message.data.user);
        options.onRoomUpdate?.(message.data.room);
        break;
        
      case 'JOIN_ROOM_SUCCESS':
        setRoom(message.data.room);
        setCurrentUser(message.data.user);
        options.onRoomUpdate?.(message.data.room);
        break;
        
      case 'USER_JOINED':
        setRoom(message.data.room);
        options.onUserJoined?.(message.data.user);
        break;
        
      case 'USER_LEFT':
        setRoom(message.data.room);
        options.onUserLeft?.(message.data.user);
        break;
        
      case 'GAME_STARTED':
        setGameState(message.data.gameState);
        options.onGameStateUpdate?.(message.data.gameState);
        break;
        
      case 'GAME_STATE_UPDATE':
        setGameState(message.data.gameState);
        options.onGameStateUpdate?.(message.data.gameState);
        break;
        
      case 'ERROR':
        setError(message.data.message || 'An error occurred');
        options.onError?.(message.data.message || 'An error occurred');
        break;
        
      default:
        console.log('Unhandled message type:', message.type);
    }
  }, [options]);

  // Game Actions
  const createRoom = useCallback((data: CreateRoomData) => {
    sendMessage({
      type: 'CREATE_ROOM',
      data,
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const joinRoom = useCallback((data: JoinRoomData) => {
    sendMessage({
      type: 'JOIN_ROOM',
      data,
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const leaveRoom = useCallback(() => {
    sendMessage({
      type: 'LEAVE_ROOM',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const startGame = useCallback(() => {
    sendMessage({
      type: 'START_GAME',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const submitAnswer = useCallback((answer: string) => {
    sendMessage({
      type: 'SUBMIT_ANSWER',
      data: { answer },
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const toggleReady = useCallback(() => {
    sendMessage({
      type: 'TOGGLE_READY',
      timestamp: Date.now()
    });
  }, [sendMessage]);

  const updateSettings = useCallback((settings: UpdateSettingsData) => {
    sendMessage({
      type: 'UPDATE_SETTINGS',
      data: settings,
      timestamp: Date.now()
    });
  }, [sendMessage]);

  // Auto-connect on mount
  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    // State
    isConnected,
    room,
    currentUser,
    gameState,
    error,
    
    // Actions
    connect,
    disconnect,
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    submitAnswer,
    toggleReady,
    updateSettings,
  };
}
