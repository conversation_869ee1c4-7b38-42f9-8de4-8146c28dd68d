# Multiplayer Setup Guide

This guide will help you integrate the flags.games server into your Next.js app for multiplayer functionality.

## 1. <PERSON><PERSON> and Set Up the Server

First, clone the server repository in a separate directory:

```bash
# Navigate to your projects directory (outside of your current app)
cd ..

# Clone the server repository
git clone https://github.com/Xurify/flags.games_server.git

# Navigate to the server directory
cd flags.games_server

# Install dependencies
pnpm install

# Start the server
pnpm dev
```

The server will run on `http://localhost:3001` by default.

## 2. What's Been Added to Your App

I've created the following files to integrate multiplayer functionality:

### Type Definitions
- `lib/types/socket.ts` - Socket and WebSocket message types
- `lib/types/multiplayer.ts` - Room, User, and game state types

### Hooks and API
- `lib/hooks/useFlagsGame.ts` - React hook for WebSocket communication
- `lib/api/flags-api.ts` - REST API client for server stats and health

### UI Components
- `components/multiplayer/CreateRoom.tsx` - Component to create new rooms
- `components/multiplayer/JoinRoom.tsx` - Component to join existing rooms
- `app/multiplayer/page.tsx` - Main multiplayer lobby page

### Configuration
- Updated `.env.local` with server URLs

## 3. How to Use

### Start the Server
1. Make sure the server is running on port 3001
2. The server provides both WebSocket and REST API endpoints

### Access Multiplayer
1. Navigate to `/multiplayer` in your app
2. Create a new room or join an existing one with an invite code
3. The WebSocket connection will handle real-time communication

## 4. Current Implementation Status

✅ **Completed:**
- Type definitions for all multiplayer entities
- WebSocket client hook with auto-reconnection
- REST API client for server communication
- Basic UI components for room creation/joining
- Environment configuration

🚧 **Next Steps (you can implement these):**
- Game room component with player list and game controls
- Real-time game state synchronization
- Answer submission and scoring
- Game settings management
- Error handling and user feedback

## 5. Architecture Overview

### Client-Server Communication
- **WebSocket**: Real-time game events, room updates, user actions
- **REST API**: Server health, statistics, room listings

### Message Flow
1. User creates/joins room via WebSocket
2. Server manages room state and broadcasts updates
3. Game state changes are synchronized in real-time
4. Answers are submitted and scored on the server

### Key Components
- `useFlagsGame` hook manages WebSocket connection and state
- `flagsApi` client handles REST API calls
- UI components provide user interface for multiplayer features

## 6. Testing the Integration

1. Start the server: `cd ../flags.games_server && pnpm dev`
2. Start your Next.js app: `pnpm dev`
3. Navigate to `http://localhost:3000/multiplayer`
4. Try creating a room and check browser console for connection logs

## 7. Troubleshooting

### Server Connection Issues
- Ensure the server is running on port 3001
- Check browser console for WebSocket connection errors
- Verify environment variables in `.env.local`

### CORS Issues
- The server should handle CORS automatically
- If issues persist, check server logs

### Type Errors
- Make sure all new type files are properly imported
- Run `pnpm build` to check for TypeScript errors

## 8. Next Development Steps

To complete the multiplayer integration, you'll want to:

1. **Create a GameRoom component** that shows:
   - Player list with ready status
   - Game controls (start, pause, settings)
   - Current game state and scores

2. **Integrate with your existing game logic**:
   - Adapt `FlagGameClient` for multiplayer mode
   - Sync question generation with server
   - Handle multiplayer scoring

3. **Add real-time features**:
   - Live player status updates
   - Real-time answer submission
   - Game state synchronization

4. **Enhance UI/UX**:
   - Loading states and error handling
   - Room settings configuration
   - Player management (kick, promote admin)

The foundation is now in place - you can build upon these components to create a full multiplayer experience!
